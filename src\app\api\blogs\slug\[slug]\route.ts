import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { corsResponse, corsOptionsResponse } from '@/lib/cors'

export async function OPTIONS() {
  return corsOptionsResponse()
}

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Public endpoint - no authentication required
    const blog = await prisma.blog.findUnique({
      where: { 
        slug: params.slug,
        status: 'PUBLISHED' // Only return published blogs
      },
      include: {
        category: true,
        tags: {
          include: {
            tag: true
          }
        }
      }
    })

    if (!blog) {
      return corsResponse({ error: 'Blog post not found' }, { status: 404 })
    }

    return corsResponse(blog)
  } catch (error) {
    console.error('Error fetching blog by slug:', error)
    return corsResponse({ error: 'Internal server error' }, { status: 500 })
  }
}
