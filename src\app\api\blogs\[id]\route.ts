import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateSlug } from '@/lib/utils'
import { z } from 'zod'
import { corsResponse, corsOptionsResponse } from '@/lib/cors'

const blogUpdateSchema = z.object({
  title: z.string().min(1, 'Title is required').optional(),
  excerpt: z.string().optional(),
  content: z.string().min(1, 'Content is required').optional(),
  author: z.string().min(1, 'Author is required').optional(),
  featured: z.boolean().optional(),
  image: z.string().optional(),
  readTime: z.string().optional(),
  status: z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED']).optional(),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  keywords: z.array(z.string()).optional(),
  categoryId: z.string().optional(),
  tagIds: z.array(z.string()).optional(),
})

export async function OPTIONS() {
  return corsOptionsResponse()
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Public endpoint - no authentication required for reading published blogs
    const { id } = await params

    const blog = await prisma.blog.findUnique({
      where: { id },
      include: {
        category: true,
        tags: {
          include: {
            tag: true,
          },
        },
      },
    })

    if (!blog) {
      return corsResponse({ error: 'Blog not found' }, { status: 404 })
    }

    return corsResponse(blog)
  } catch (error) {
    console.error('Error fetching blog:', error)
    return corsResponse({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = blogUpdateSchema.parse(body)
    const { id } = await params

    const existingBlog = await prisma.blog.findUnique({
      where: { id },
    })

    if (!existingBlog) {
      return NextResponse.json({ error: 'Blog not found' }, { status: 404 })
    }

    let slug = existingBlog.slug
    
    // Generate new slug if title changed
    if (validatedData.title && validatedData.title !== existingBlog.title) {
      slug = generateSlug(validatedData.title)
      
      // Check if new slug already exists
      const slugExists = await prisma.blog.findFirst({
        where: {
          slug,
          id: { not: id }
        },
      })
      
      if (slugExists) {
        return NextResponse.json({ error: 'A blog with this title already exists' }, { status: 400 })
      }
    }

    const { tagIds, ...blogData } = validatedData

    // Handle status change to published
    const updateData: {
      title?: string
      excerpt?: string
      content?: string
      author?: string
      featured?: boolean
      image?: string
      readTime?: string
      status?: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED'
      metaTitle?: string
      metaDescription?: string
      keywords?: string[]
      categoryId?: string
      slug: string
      publishedAt?: Date
    } = {
      ...blogData,
      slug,
    }

    if (validatedData.status === 'PUBLISHED' && existingBlog.status !== 'PUBLISHED') {
      updateData.publishedAt = new Date()
    }

    const blog = await prisma.blog.update({
      where: { id },
      data: {
        ...updateData,
        ...(tagIds && {
          tags: {
            deleteMany: {},
            create: tagIds.map((tagId) => ({
              tag: { connect: { id: tagId } },
            })),
          },
        }),
      },
      include: {
        category: true,
        tags: {
          include: {
            tag: true,
          },
        },
      },
    })

    return NextResponse.json(blog)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 })
    }
    
    console.error('Error updating blog:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const blog = await prisma.blog.findUnique({
      where: { id },
    })

    if (!blog) {
      return NextResponse.json({ error: 'Blog not found' }, { status: 404 })
    }

    await prisma.blog.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Blog deleted successfully' })
  } catch (error) {
    console.error('Error deleting blog:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
